@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components
@inherits FbAutoReplyPlatformExpressComponentBase
@inject NavigationManager NavigationManager
@inject IFacebookAuthService FacebookAuthService

<div class="facebook-error-handler">
    @if (ShowErrorAlert && !string.IsNullOrEmpty(ErrorMessage))
    {
        <Alert Color="@GetAlertColor()" Visible="true" Class="mb-3">
            <AlertMessage>
                <Icon Name="@GetAlertIcon()" class="me-2" />
                <strong>@GetAlertTitle()</strong>
            </AlertMessage>
            <AlertDescription>
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1 me-3">
                        <div>@ErrorMessage</div>
                        @if (AffectedPages.Any())
                        {
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>Affected pages:</strong> @string.Join(", ", AffectedPages.Take(3))
                                    @if (AffectedPages.Count > 3)
                                    {
                                        <span> and @(AffectedPages.Count - 3) more</span>
                                    }
                                </small>
                            </div>
                        }
                        @if (ShowTechnicalDetails && !string.IsNullOrEmpty(TechnicalDetails))
                        {
                            <details class="mt-2">
                                <summary class="text-muted" style="cursor: pointer;">Technical Details</summary>
                                <small class="text-muted mt-1 d-block">@TechnicalDetails</small>
                            </details>
                        }
                    </div>
                    <div class="d-flex flex-column gap-2">
                        @if (ShowReconnectButton)
                        {
                            <Button Color="Color.Primary" Size="Size.Small" Clicked="@HandleReconnectClicked">
                                <Icon Name="IconName.Link" class="me-1" />
                                Reconnect Facebook
                            </Button>
                        }
                        @if (ShowRetryButton)
                        {
                            <Button Color="Color.Secondary" Size="Size.Small" Clicked="@HandleRetryClicked" Disabled="@IsRetrying">
                                @if (IsRetrying)
                                {
                                    <div class="spinner-border spinner-border-sm me-1" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                }
                                else
                                {
                                    <Icon Name="IconName.Redo" class="me-1" />
                                }
                                Retry
                            </Button>
                        }
                        @if (ShowDismissButton)
                        {
                            <Button Color="Color.Light" Size="Size.Small" Clicked="@HandleDismissClicked">
                                <Icon Name="IconName.Times" />
                            </Button>
                        }
                    </div>
                </div>
            </AlertDescription>
        </Alert>
    }
</div>

@code {
    [Parameter] public FacebookErrorType ErrorType { get; set; } = FacebookErrorType.General;
    [Parameter] public string ErrorMessage { get; set; } = string.Empty;
    [Parameter] public string TechnicalDetails { get; set; } = string.Empty;
    [Parameter] public List<string> AffectedPages { get; set; } = new();
    [Parameter] public bool ShowErrorAlert { get; set; } = true;
    [Parameter] public bool ShowReconnectButton { get; set; } = true;
    [Parameter] public bool ShowRetryButton { get; set; } = false;
    [Parameter] public bool ShowDismissButton { get; set; } = true;
    [Parameter] public bool ShowTechnicalDetails { get; set; } = false;
    [Parameter] public bool AutoRedirectOnCritical { get; set; } = true;
    [Parameter] public EventCallback OnReconnectClicked { get; set; }
    [Parameter] public EventCallback OnRetryClicked { get; set; }
    [Parameter] public EventCallback OnDismissClicked { get; set; }

    private bool IsRetrying { get; set; } = false;

    protected override async Task OnParametersSetAsync()
    {
        // Auto-redirect for critical errors
        if (AutoRedirectOnCritical && IsCriticalError() && ShowErrorAlert)
        {
            await Task.Delay(5000); // Wait 5 seconds before auto-redirect
            if (ShowErrorAlert) // Check if still showing error
            {
                NavigationManager.NavigateTo("/facebook/connection");
            }
        }
    }

    private Color GetAlertColor()
    {
        return ErrorType switch
        {
            FacebookErrorType.TokenExpired => Color.Danger,
            FacebookErrorType.TokenInvalid => Color.Danger,
            FacebookErrorType.MissingPermissions => Color.Warning,
            FacebookErrorType.ConnectionLost => Color.Warning,
            FacebookErrorType.RateLimited => Color.Info,
            FacebookErrorType.General => Color.Secondary,
            _ => Color.Secondary
        };
    }

    private IconName GetAlertIcon()
    {
        return ErrorType switch
        {
            FacebookErrorType.TokenExpired => IconName.ExclamationTriangle,
            FacebookErrorType.TokenInvalid => IconName.Times,
            FacebookErrorType.MissingPermissions => IconName.ExclamationTriangle,
            FacebookErrorType.ConnectionLost => IconName.Unlink,
            FacebookErrorType.RateLimited => IconName.Clock,
            FacebookErrorType.General => IconName.Info,
            _ => IconName.Info
        };
    }

    private string GetAlertTitle()
    {
        return ErrorType switch
        {
            FacebookErrorType.TokenExpired => "Facebook Token Expired",
            FacebookErrorType.TokenInvalid => "Facebook Token Invalid",
            FacebookErrorType.MissingPermissions => "Missing Facebook Permissions",
            FacebookErrorType.ConnectionLost => "Facebook Connection Lost",
            FacebookErrorType.RateLimited => "Facebook Rate Limit Reached",
            FacebookErrorType.General => "Facebook Integration Issue",
            _ => "Facebook Integration Issue"
        };
    }

    private bool IsCriticalError()
    {
        return ErrorType == FacebookErrorType.TokenExpired || 
               ErrorType == FacebookErrorType.TokenInvalid || 
               ErrorType == FacebookErrorType.ConnectionLost;
    }

    private async Task HandleReconnectClicked()
    {
        if (OnReconnectClicked.HasDelegate)
        {
            await OnReconnectClicked.InvokeAsync();
        }
        else
        {
            NavigationManager.NavigateTo("/facebook/connection");
        }
    }

    private async Task HandleRetryClicked()
    {
        if (OnRetryClicked.HasDelegate)
        {
            IsRetrying = true;
            StateHasChanged();
            
            try
            {
                await OnRetryClicked.InvokeAsync();
            }
            finally
            {
                IsRetrying = false;
                StateHasChanged();
            }
        }
    }

    private async Task HandleDismissClicked()
    {
        ShowErrorAlert = false;
        StateHasChanged();
        
        if (OnDismissClicked.HasDelegate)
        {
            await OnDismissClicked.InvokeAsync();
        }
    }
}



<style>
    .facebook-error-handler .alert {
        border-left: 4px solid;
    }
    
    .facebook-error-handler .alert-danger {
        border-left-color: #dc3545;
    }
    
    .facebook-error-handler .alert-warning {
        border-left-color: #ffc107;
    }
    
    .facebook-error-handler .alert-info {
        border-left-color: #0dcaf0;
    }
    
    .facebook-error-handler details summary {
        font-size: 0.875rem;
    }
    
    .facebook-error-handler details[open] summary {
        margin-bottom: 0.5rem;
    }
</style>
