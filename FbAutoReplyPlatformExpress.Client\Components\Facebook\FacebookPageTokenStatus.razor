@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookAuthService FacebookAuthService
@inject NavigationManager NavigationManager

<div class="facebook-page-token-status">
    @if (TokenStatus != null)
    {
        @if (TokenStatus.IsTokenValid && !TokenStatus.RequiresReconnection)
        {
            <!-- Valid Token -->
            <Badge Color="Color.Success" Class="me-1">
                <Icon Name="IconName.CheckCircle" class="me-1" />
                Valid
            </Badge>
            @if (TokenStatus.TokenExpiresAt.HasValue)
            {
                <small class="text-muted">
                    Expires: @TokenStatus.TokenExpiresAt.Value.ToString("MMM dd")
                </small>
            }
        }
        else if (TokenStatus.IsTokenExpired)
        {
            <!-- Expired Token -->
            <Badge Color="Color.Danger" Class="me-1">
                <Icon Name="IconName.ExclamationTriangle" class="me-1" />
                Expired
            </Badge>
            @if (ShowReconnectButton)
            {
                <Button Color="Color.Primary" Size="Size.ExtraSmall" Clicked="@HandleReconnectClicked">
                    Reconnect
                </Button>
            }
        }
        else if (TokenStatus.MissingScopes.Any())
        {
            <!-- Missing Scopes -->
            <Badge Color="Color.Warning" Class="me-1">
                <Icon Name="IconName.ExclamationTriangle" class="me-1" />
                Missing Permissions
            </Badge>
            @if (ShowReconnectButton)
            {
                <Button Color="Color.Primary" Size="Size.ExtraSmall" Clicked="@HandleReconnectClicked">
                    Reconnect
                </Button>
            }
        }
        else if (!TokenStatus.IsTokenValid)
        {
            <!-- Invalid Token -->
            <Badge Color="Color.Danger" Class="me-1">
                <Icon Name="IconName.Times" class="me-1" />
                Invalid
            </Badge>
            @if (ShowReconnectButton)
            {
                <Button Color="Color.Primary" Size="Size.ExtraSmall" Clicked="@HandleReconnectClicked">
                    Reconnect
                </Button>
            }
        }
        
        @if (!string.IsNullOrEmpty(TokenStatus.ErrorMessage) && ShowErrorDetails)
        {
            <div class="mt-1">
                <small class="text-danger">@TokenStatus.ErrorMessage</small>
            </div>
        }
        
        @if (TokenStatus.MissingScopes.Any() && ShowScopeDetails)
        {
            <div class="mt-1">
                <small class="text-warning">
                    Missing: @string.Join(", ", TokenStatus.MissingScopes)
                </small>
            </div>
        }
    }
    else if (IsLoading)
    {
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <small class="text-muted">Validating...</small>
        </div>
    }
    else
    {
        <Badge Color="Color.Secondary">
            <Icon Name="IconName.Question" class="me-1" />
            Unknown
        </Badge>
    }
</div>

@code {
    [Parameter] public Guid PageId { get; set; }
    [Parameter] public FacebookPageTokenStatusDto? TokenStatus { get; set; }
    [Parameter] public bool ShowReconnectButton { get; set; } = true;
    [Parameter] public bool ShowErrorDetails { get; set; } = false;
    [Parameter] public bool ShowScopeDetails { get; set; } = false;
    [Parameter] public bool AutoValidate { get; set; } = true;
    [Parameter] public EventCallback<FacebookPageTokenStatusDto> OnStatusChanged { get; set; }
    [Parameter] public EventCallback OnReconnectClicked { get; set; }

    private bool IsLoading { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {
        if (AutoValidate && TokenStatus == null && PageId != Guid.Empty)
        {
            await ValidateTokenAsync();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (AutoValidate && TokenStatus == null && PageId != Guid.Empty)
        {
            await ValidateTokenAsync();
        }
    }

    public async Task ValidateTokenAsync()
    {
        if (PageId == Guid.Empty) return;

        try
        {
            IsLoading = true;
            StateHasChanged();

            TokenStatus = await FacebookAuthService.ValidatePageTokenAsync(PageId);
            
            if (OnStatusChanged.HasDelegate)
            {
                await OnStatusChanged.InvokeAsync(TokenStatus);
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error validating page token: {ex.Message}");
            TokenStatus = new FacebookPageTokenStatusDto
            {
                PageId = PageId,
                PageName = "Unknown",
                IsTokenValid = false,
                ErrorMessage = "Validation failed",
                RequiresReconnection = true
            };
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleReconnectClicked()
    {
        if (OnReconnectClicked.HasDelegate)
        {
            await OnReconnectClicked.InvokeAsync();
        }
        else
        {
            // Default behavior: navigate to connection page
            NavigationManager.NavigateTo("/facebook/connection");
        }
    }
}

<style>
    .facebook-page-token-status {
        display: inline-flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .facebook-page-token-status .badge {
        display: inline-flex;
        align-items: center;
    }
    
    .facebook-page-token-status .btn-xs {
        font-size: 0.75rem;
        padding: 0.125rem 0.375rem;
    }
</style>
