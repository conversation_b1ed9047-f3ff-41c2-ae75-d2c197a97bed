@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookAuthService FacebookAuthService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<div class="facebook-action-buttons">
    @if (IsProcessing)
    {
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Processing...</span>
            </div>
            <span>@ProcessingMessage</span>
        </div>
    }
    else
    {
        <div class="d-flex gap-2 flex-wrap">
            @if (ShowConnectButton)
            {
                <Button Color="Color.Primary" Clicked="@ConnectFacebookAsync" Disabled="@IsProcessing">
                    <Icon Name="IconName.Link" class="me-1" />
                    Connect Facebook
                </Button>
            }

            @if (ShowReconnectButton)
            {
                <Button Color="Color.Warning" Clicked="@ReconnectFacebookAsync" Disabled="@IsProcessing">
                    <Icon Name="IconName.Refresh" class="me-1" />
                    Reconnect Facebook
                </Button>
            }

            @if (ShowValidateButton)
            {
                <Button Color="Color.Info" Outline="true" Clicked="@ValidateTokenAsync" Disabled="@IsProcessing">
                    <Icon Name="IconName.Shield" class="me-1" />
                    Validate Token
                </Button>
            }

            @if (ShowDisconnectButton)
            {
                <Button Color="Color.Secondary" Outline="true" Clicked="@DisconnectFacebookAsync" Disabled="@IsProcessing">
                    <Icon Name="IconName.Unlink" class="me-1" />
                    Disconnect
                </Button>
            }

            @if (ShowRevokeButton)
            {
                <Button Color="Color.Danger" Outline="true" Clicked="@RevokePermissionsAsync" Disabled="@IsProcessing">
                    <Icon Name="IconName.Times" class="me-1" />
                    Remove Permissions
                </Button>
            }
        </div>
    }
</div>

@code {
    [Parameter] public bool ShowConnectButton { get; set; } = false;
    [Parameter] public bool ShowReconnectButton { get; set; } = false;
    [Parameter] public bool ShowValidateButton { get; set; } = false;
    [Parameter] public bool ShowDisconnectButton { get; set; } = false;
    [Parameter] public bool ShowRevokeButton { get; set; } = false;
    [Parameter] public EventCallback OnActionCompleted { get; set; }
    [Parameter] public EventCallback<string> OnError { get; set; }

    private bool IsProcessing { get; set; } = false;
    private string ProcessingMessage { get; set; } = string.Empty;

    private async Task ConnectFacebookAsync()
    {
        try
        {
            IsProcessing = true;
            ProcessingMessage = "Redirecting to Facebook...";
            StateHasChanged();

            var redirectUri = GetRedirectUri();
            var loginUrl = await FacebookAuthService.GetFacebookLoginUrlAsync(redirectUri);

            await JSRuntime.InvokeVoidAsync("open", loginUrl, "_self");
        }
        catch (Exception ex)
        {
            await HandleError($"Failed to initiate Facebook connection: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
            StateHasChanged();
        }
    }

    private async Task ReconnectFacebookAsync()
    {
        try
        {
            IsProcessing = true;
            ProcessingMessage = "Redirecting to Facebook for reconnection...";
            StateHasChanged();

            var redirectUri = GetRedirectUri();
            var loginUrl = await FacebookAuthService.GetFacebookLoginUrlAsync(redirectUri);

            await JSRuntime.InvokeVoidAsync("open", loginUrl, "_self");
        }
        catch (Exception ex)
        {
            await HandleError($"Failed to initiate Facebook reconnection: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
            StateHasChanged();
        }
    }

    private async Task ValidateTokenAsync()
    {
        try
        {
            IsProcessing = true;
            ProcessingMessage = "Validating Facebook token...";
            StateHasChanged();

            var validation = await FacebookAuthService.ValidateCurrentTokenAsync();
            
            if (validation.IsValid && !validation.IsExpired)
            {
                await Message.Success("Facebook token is valid and active!");
            }
            else if (validation.IsExpired)
            {
                await Message.Warning("Facebook token has expired. Please reconnect your account.");
            }
            else
            {
                var errorMsg = !string.IsNullOrEmpty(validation.ErrorMessage) 
                    ? validation.ErrorMessage 
                    : "Facebook token is invalid";
                await Message.Error($"Token validation failed: {errorMsg}");
            }

            if (OnActionCompleted.HasDelegate)
            {
                await OnActionCompleted.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            await HandleError($"Error validating Facebook token: {ex.Message}");
        }
        finally
        {
            IsProcessing = false;
            StateHasChanged();
        }
    }

    private async Task DisconnectFacebookAsync()
    {
        var confirmed = await Message.Confirm(
            "Are you sure you want to disconnect your Facebook account?",
            "Disconnect Facebook",
            options => options.OkButtonText = "Disconnect");

        if (confirmed)
        {
            try
            {
                IsProcessing = true;
                ProcessingMessage = "Disconnecting Facebook account...";
                StateHasChanged();

                await FacebookAuthService.DisconnectFacebookAsync();
                await Message.Success("Facebook account disconnected successfully!");

                if (OnActionCompleted.HasDelegate)
                {
                    await OnActionCompleted.InvokeAsync();
                }
            }
            catch (Exception ex)
            {
                await HandleError($"Failed to disconnect Facebook account: {ex.Message}");
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }
    }

    private async Task RevokePermissionsAsync()
    {
        var confirmed = await Message.Confirm(
            "This will completely remove your Facebook app permissions and disconnect your account. You will need to reconnect and re-authorize all permissions. Are you sure?",
            "Remove Facebook Permissions",
            options => 
            {
                options.OkButtonText = "Remove Permissions";
                options.OkButtonColor = Color.Danger;
            });

        if (confirmed)
        {
            try
            {
                IsProcessing = true;
                ProcessingMessage = "Removing Facebook permissions...";
                StateHasChanged();

                var success = await FacebookAuthService.RevokePermissionsAsync();
                
                if (success)
                {
                    await Message.Success("Facebook permissions removed successfully!");
                }
                else
                {
                    await Message.Warning("Facebook permissions may not have been fully removed. Your local connection has been disconnected.");
                }

                if (OnActionCompleted.HasDelegate)
                {
                    await OnActionCompleted.InvokeAsync();
                }
            }
            catch (Exception ex)
            {
                await HandleError($"Error removing Facebook permissions: {ex.Message}");
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }
    }

    private async Task HandleError(string errorMessage)
    {
        await Message.Error(errorMessage);
        
        if (OnError.HasDelegate)
        {
            await OnError.InvokeAsync(errorMessage);
        }
    }

    private string GetRedirectUri()
    {
        var baseUri = NavigationManager.BaseUri.TrimEnd('/');
        return $"{baseUri}/facebook/connection";
    }
}

<style>
    .facebook-action-buttons {
        margin: 1rem 0;
    }
    
    .facebook-action-buttons .btn {
        white-space: nowrap;
    }
</style>
