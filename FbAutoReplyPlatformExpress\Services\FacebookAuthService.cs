using System;
using System.Linq;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Permissions;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace FbAutoReplyPlatformExpress.Services;

[Authorize]
public class FacebookAuthService : ApplicationService, IFacebookAuthService
{
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<FacebookAuthService> _logger;

    public FacebookAuthService(
        IRepository<FacebookUser, Guid> facebookUserRepository,
        FacebookGraphApiService facebookGraphApiService,
        ILogger<FacebookAuthService> logger)
    {
        _facebookUserRepository = facebookUserRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> GetCurrentUserFacebookInfoAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);
        
        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected.");
        }

        return ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> CreateOrUpdateFacebookUserAsync(CreateFacebookUserDto input)
    {
        var currentUserId = CurrentUser.GetId();
        var existingFacebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (existingFacebookUser != null)
        {
            // Update existing
            existingFacebookUser.UpdateTokens(input.AccessToken, input.RefreshToken, input.TokenExpiresAt);
            existingFacebookUser.UpdateProfile(input.FacebookName, input.ProfilePictureUrl ?? string.Empty);
            existingFacebookUser.Activate();
            
            await _facebookUserRepository.UpdateAsync(existingFacebookUser);
            return ObjectMapper.Map<FacebookUser, FacebookUserDto>(existingFacebookUser);
        }
        else
        {
            // Create new
            var facebookUser = new FacebookUser(
                GuidGenerator.Create(),
                input.FacebookId,
                currentUserId,
                input.AccessToken,
                input.FacebookEmail,
                input.FacebookName);

            if (input.RefreshToken != null)
            {
                facebookUser.UpdateTokens(input.AccessToken, input.RefreshToken, input.TokenExpiresAt);
            }

            if (!string.IsNullOrEmpty(input.ProfilePictureUrl))
            {
                facebookUser.UpdateProfile(input.FacebookName, input.ProfilePictureUrl);
            }

            await _facebookUserRepository.InsertAsync(facebookUser);
            return ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);
        }
    }

    public async Task<string> GetFacebookLoginUrlAsync(string redirectUri)
    {
        return _facebookGraphApiService.GetFacebookLoginUrl(redirectUri);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> HandleFacebookCallbackAsync(string code, string redirectUri)
    {
        try
        {
            // Exchange code for access token
            var tokenResponse = await _facebookGraphApiService.ExchangeCodeForTokenAsync(code, redirectUri);
            
            // Get user info from Facebook
            var userInfo = await _facebookGraphApiService.GetUserInfoAsync(tokenResponse.AccessToken);
            
            // Create or update Facebook user
            var createDto = new CreateFacebookUserDto
            {
                FacebookId = userInfo.Id,
                UserId = CurrentUser.GetId(),
                AccessToken = tokenResponse.AccessToken,
                FacebookEmail = userInfo.Email ?? string.Empty,
                FacebookName = userInfo.Name,
                ProfilePictureUrl = userInfo.Picture?.Data?.Url,
                TokenExpiresAt = tokenResponse.ExpiresIn.HasValue 
                    ? DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn.Value) 
                    : null
            };

            return await CreateOrUpdateFacebookUserAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling Facebook callback");
            throw new UserFriendlyException("Failed to connect Facebook account. Please try again.");
        }
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task RefreshFacebookTokenAsync(Guid facebookUserId)
    {
        var facebookUser = await _facebookUserRepository.GetAsync(facebookUserId);
        
        if (facebookUser.UserId != CurrentUser.GetId())
        {
            throw new UnauthorizedAccessException();
        }

        // Note: Facebook doesn't provide refresh tokens for user access tokens
        // This method would be used if we implement long-lived tokens
        // For now, we'll just validate the current token
        try
        {
            await _facebookGraphApiService.GetUserInfoAsync(facebookUser.AccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Facebook token validation failed for user {UserId}", facebookUser.UserId);
            throw new UserFriendlyException("Facebook token has expired. Please reconnect your account.");
        }
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Disconnect)]
    public async Task DisconnectFacebookAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);
        
        if (facebookUser != null)
        {
            facebookUser.Deactivate();
            await _facebookUserRepository.UpdateAsync(facebookUser);
        }
    }
    [HttpGet]
    public async Task<bool> IsConnectedToFacebookAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);
        return facebookUser != null;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookConnectionStatusDto> GetConnectionStatusAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        var status = new FacebookConnectionStatusDto
        {
            IsConnected = facebookUser != null && facebookUser.IsActive
        };

        if (facebookUser == null)
        {
            return status;
        }

        status.FacebookUser = ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);

        // Validate the access token
        try
        {
            var validationResult = await _facebookGraphApiService.ValidateAccessTokenAsync(facebookUser.AccessToken);

            status.IsTokenValid = validationResult.IsValid;
            status.IsTokenExpired = validationResult.IsExpired;
            status.TokenExpiresAt = validationResult.ExpiresAt;
            status.MissingScopes = validationResult.MissingScopes;
            status.ErrorMessage = validationResult.ErrorMessage;
            status.RequiresReconnection = !validationResult.IsValid || validationResult.IsExpired || validationResult.MissingScopes.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Facebook token for user {UserId}", currentUserId);
            status.IsTokenValid = false;
            status.RequiresReconnection = true;
            status.ErrorMessage = "Unable to validate Facebook connection. Please reconnect your account.";
        }

        return status;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> ReconnectFacebookAccountAsync(string redirectUri)
    {
        // This method initiates the reconnection flow by returning the login URL
        // The actual reconnection happens in HandleFacebookCallbackAsync
        var loginUrl = await GetFacebookLoginUrlAsync(redirectUri);

        // For now, we'll throw an exception with the login URL
        // In a real implementation, this might redirect or return the URL differently
        throw new UserFriendlyException($"Please visit this URL to reconnect your Facebook account: {loginUrl}");
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Disconnect)]
    public async Task<bool> RevokePermissionsAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            return false;
        }

        try
        {
            // Revoke permissions on Facebook
            var revoked = await _facebookGraphApiService.RevokeUserPermissionsAsync(facebookUser.AccessToken);

            if (revoked)
            {
                // Deactivate the local Facebook user record
                facebookUser.Deactivate();
                await _facebookUserRepository.UpdateAsync(facebookUser);

                _logger.LogInformation("Successfully revoked Facebook permissions for user {UserId}", currentUserId);
                return true;
            }
            else
            {
                _logger.LogWarning("Failed to revoke Facebook permissions for user {UserId}", currentUserId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking Facebook permissions for user {UserId}", currentUserId);
            return false;
        }
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookTokenValidationDto> ValidateCurrentTokenAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (facebookUser == null)
        {
            return new FacebookTokenValidationDto
            {
                IsValid = false,
                ErrorMessage = "No Facebook account connected"
            };
        }

        try
        {
            var validationResult = await _facebookGraphApiService.ValidateAccessTokenAsync(facebookUser.AccessToken);

            return new FacebookTokenValidationDto
            {
                IsValid = validationResult.IsValid,
                IsExpired = validationResult.IsExpired,
                ExpiresAt = validationResult.ExpiresAt,
                Scopes = validationResult.Scopes,
                MissingScopes = validationResult.MissingScopes,
                ErrorMessage = validationResult.ErrorMessage,
                ErrorCode = validationResult.ErrorCode,
                TokenType = validationResult.TokenType
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Facebook token for user {UserId}", currentUserId);
            return new FacebookTokenValidationDto
            {
                IsValid = false,
                ErrorMessage = "Failed to validate token: " + ex.Message
            };
        }
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task TriggerTokenValidationAsync()
    {
        try
        {
            var tokenValidationJob = LazyServiceProvider.LazyGetRequiredService<FacebookTokenValidationJob>();
            await tokenValidationJob.ValidateTokensAsync();
            _logger.LogInformation("Manual token validation completed for user {UserId}", CurrentUser.GetId());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual token validation for user {UserId}", CurrentUser.GetId());
            throw new UserFriendlyException("Failed to validate tokens. Please try again later.");
        }
    }
}
