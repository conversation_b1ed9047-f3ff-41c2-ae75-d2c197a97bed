@page "/facebook/connection"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Components.Web
@using FbAutoReplyPlatformExpress.Client.Components.Facebook
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookAuthService FacebookAuthService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]

<PageTitle>Facebook Connection</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.Link" />
                    Facebook Connection
                </h2>
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                @if (ConnectionStatus?.IsConnected == true)
                {
                    <Button Color="Color.Info" Size="Size.Small" Outline="true" Clicked="@RefreshStatusAsync">
                        <Icon Name="IconName.Refresh" class="me-1" />
                        Refresh Status
                    </Button>
                }
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        <!-- Facebook Connection Status Component -->
        <FacebookConnectionStatus @ref="connectionStatusComponent"
                                  OnConnectClicked="@HandleConnectClicked"
                                  OnReconnectClicked="@HandleReconnectClicked"
                                  OnDisconnectClicked="@HandleDisconnectClicked"
                                  OnRevokePermissionsClicked="@HandleRevokePermissionsClicked"
                                  OnStatusChanged="@HandleStatusChanged" />

        <!-- Additional Actions Section -->
        @if (ConnectionStatus?.IsConnected == true)
        {
            <Card Class="mt-3">
                <CardHeader>
                    <h5 class="mb-0">
                        <Icon Name="IconName.Cog" class="me-2" />
                        Advanced Actions
                    </h5>
                </CardHeader>
                <CardBody>
                    <FacebookActionButtons ShowValidateButton="true"
                                           ShowDisconnectButton="true"
                                           ShowRevokeButton="true"
                                           OnActionCompleted="@HandleActionCompleted"
                                           OnError="@HandleActionError" />

                    <div class="mt-3">
                        <Button Color="Color.Primary" Clicked="NavigateToPages" Class="me-2">
                            <Icon Name="IconName.ArrowRight" class="me-1" />
                            Manage Facebook Pages
                        </Button>
                        <Button Color="Color.Light" Outline="true" Size="Size.Small" Clicked="@(() => ShowTokenDetails = !ShowTokenDetails)">
                            <Icon Name="IconName.Shield" class="me-1" />
                            @(ShowTokenDetails ? "Hide" : "Show") Token Details
                        </Button>
                    </div>
                </CardBody>
            </Card>
        }

        <!-- Information Section for Non-Connected Users -->
        @if (ConnectionStatus?.IsConnected != true)
        {
            <Card Class="mt-3">
                <CardHeader>
                    <h5 class="mb-0">
                        <Icon Name="IconName.Info" class="me-2" />
                        What You Can Do After Connecting
                    </h5>
                </CardHeader>
                <CardBody>
                    <Row>
                        <Column ColumnSize="ColumnSize.Is12.OnDesktop.Is6.OnWidescreen">
                            <h6><Icon Name="IconName.CheckCircle" class="text-success me-2" />Page Management</h6>
                            <ul class="list-unstyled ms-4">
                                <li>• Import and manage your Facebook Pages</li>
                                <li>• View posts from your Pages</li>
                                <li>• Monitor page engagement metrics</li>
                            </ul>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is12.OnDesktop.Is6.OnWidescreen">
                            <h6><Icon Name="IconName.CheckCircle" class="text-success me-2" />Auto-Reply Features</h6>
                            <ul class="list-unstyled ms-4">
                                <li>• Create auto-reply campaigns for comments</li>
                                <li>• Send both public replies and private messages</li>
                                <li>• Monitor campaign performance and analytics</li>
                            </ul>
                        </Column>
                    </Row>
                </CardBody>
            </Card>
        }

        <!-- Token Information Section (for debugging/admin) -->
        @if (ConnectionStatus?.IsConnected == true && ShowTokenDetails)
        {
            <Card Class="mt-3">
                <CardHeader>
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <Icon Name="IconName.Shield" class="me-2" />
                            Token Information
                        </h6>
                        <Button Color="Color.Light" Size="Size.Small" Clicked="@(() => ShowTokenDetails = false)">
                            <Icon Name="IconName.Times" />
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    @if (TokenValidation != null)
                    {
                        <Row>
                            <Column ColumnSize="ColumnSize.Is6">
                                <strong>Token Status:</strong>
                                <Badge Color="@(TokenValidation.IsValid ? Color.Success : Color.Danger)">
                                    @(TokenValidation.IsValid ? "Valid" : "Invalid")
                                </Badge>
                            </Column>
                            <Column ColumnSize="ColumnSize.Is6">
                                <strong>Token Type:</strong> @TokenValidation.TokenType
                            </Column>
                        </Row>
                        @if (TokenValidation.ExpiresAt.HasValue)
                        {
                            <Row Class="mt-2">
                                <Column ColumnSize="ColumnSize.Is12">
                                    <strong>Expires:</strong> @TokenValidation.ExpiresAt.Value.ToString("MMM dd, yyyy HH:mm:ss")
                                    @if (TokenValidation.IsExpired)
                                    {
                                        <Badge Color="Color.Danger" Class="ms-2">Expired</Badge>
                                    }
                                </Column>
                            </Row>
                        }
                        @if (TokenValidation.Scopes.Any())
                        {
                            <Row Class="mt-2">
                                <Column ColumnSize="ColumnSize.Is12">
                                    <strong>Granted Scopes:</strong>
                                    <div class="mt-1">
                                        @foreach (var scope in TokenValidation.Scopes)
                                        {
                                            <Badge Color="Color.Info" Class="me-1">@scope</Badge>
                                        }
                                    </div>
                                </Column>
                            </Row>
                        }
                        @if (TokenValidation.MissingScopes.Any())
                        {
                            <Row Class="mt-2">
                                <Column ColumnSize="ColumnSize.Is12">
                                    <strong>Missing Scopes:</strong>
                                    <div class="mt-1">
                                        @foreach (var scope in TokenValidation.MissingScopes)
                                        {
                                            <Badge Color="Color.Warning" Class="me-1">@scope</Badge>
                                        }
                                    </div>
                                </Column>
                            </Row>
                        }
                    }
                    <div class="mt-3">
                        <Button Color="Color.Info" Size="Size.Small" Clicked="@ValidateTokenDetailsAsync">
                            <Icon Name="IconName.Refresh" class="me-1" />
                            Refresh Token Info
                        </Button>
                    </div>
                </CardBody>
            </Card>
        }

        @if (IsLoading)
        {
            <div class="text-center mt-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">@LoadingMessage</p>
            </div>
        }
    </CardBody>
</Card>

@code {
    private FacebookConnectionStatus? connectionStatusComponent;
    private FacebookConnectionStatusDto? ConnectionStatus { get; set; }
    private FacebookTokenValidationDto? TokenValidation { get; set; }
    private bool IsLoading { get; set; } = false;
    private string LoadingMessage { get; set; } = "Loading...";
    private bool ShowTokenDetails { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Check for Facebook callback
            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var query = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

            if (query.ContainsKey("code"))
            {
                await HandleFacebookCallback(query);
            }
        }
    }

    private async Task LoadInitialData()
    {
        try
        {
            IsLoading = true;
            LoadingMessage = "Loading Facebook connection status...";
            StateHasChanged();

            // The FacebookConnectionStatus component will load its own data
            // We just need to wait a moment for it to initialize
            await Task.Delay(100);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleFacebookCallback(IDictionary<string, Microsoft.Extensions.Primitives.StringValues> query)
    {
        IsLoading = true;
        LoadingMessage = "Processing Facebook connection...";
        StateHasChanged();

        try
        {
            var code = query["code"].ToString();
            var redirectUri = GetRedirectUri();

            var facebookUser = await FacebookAuthService.HandleFacebookCallbackAsync(code, redirectUri);
            await Message.Success("Facebook account connected successfully!");

            // Refresh the connection status component
            if (connectionStatusComponent != null)
            {
                await connectionStatusComponent.RefreshAsync();
            }

            // Clean up URL
            NavigationManager.NavigateTo("/facebook/connection", replace: true);
        }
        catch (Exception ex)
        {
            await Message.Error($"Failed to connect Facebook account: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleConnectClicked()
    {
        try
        {
            var redirectUri = GetRedirectUri();
            var loginUrl = await FacebookAuthService.GetFacebookLoginUrlAsync(redirectUri);
            await JSRuntime.InvokeVoidAsync("open", loginUrl, "_self");
        }
        catch (Exception ex)
        {
            await Message.Error($"Failed to initiate Facebook connection: {ex.Message}");
        }
    }

    private async Task HandleReconnectClicked()
    {
        try
        {
            var redirectUri = GetRedirectUri();
            var loginUrl = await FacebookAuthService.GetFacebookLoginUrlAsync(redirectUri);
            await JSRuntime.InvokeVoidAsync("open", loginUrl, "_self");
        }
        catch (Exception ex)
        {
            await Message.Error($"Failed to initiate Facebook reconnection: {ex.Message}");
        }
    }

    private async Task HandleDisconnectClicked()
    {
        var confirmed = await Message.Confirm("Are you sure you want to disconnect your Facebook account?");
        if (confirmed)
        {
            try
            {
                await FacebookAuthService.DisconnectFacebookAsync();
                await Message.Success("Facebook account disconnected successfully!");

                if (connectionStatusComponent != null)
                {
                    await connectionStatusComponent.RefreshAsync();
                }
            }
            catch (Exception ex)
            {
                await Message.Error($"Failed to disconnect Facebook account: {ex.Message}");
            }
        }
    }

    private async Task HandleRevokePermissionsClicked()
    {
        var confirmed = await Message.Confirm(
            "This will completely remove your Facebook app permissions and disconnect your account. You will need to reconnect and re-authorize all permissions. Are you sure?",
            "Remove Facebook Permissions",
            options =>
            {
                options.OkButtonText = "Remove Permissions";
                options.OkButtonColor = Color.Danger;
            });

        if (confirmed)
        {
            try
            {
                var success = await FacebookAuthService.RevokePermissionsAsync();

                if (success)
                {
                    await Message.Success("Facebook permissions removed successfully!");
                }
                else
                {
                    await Message.Warning("Facebook permissions may not have been fully removed. Your local connection has been disconnected.");
                }

                if (connectionStatusComponent != null)
                {
                    await connectionStatusComponent.RefreshAsync();
                }
            }
            catch (Exception ex)
            {
                await Message.Error($"Error removing Facebook permissions: {ex.Message}");
            }
        }
    }

    private async Task HandleStatusChanged(FacebookConnectionStatusDto status)
    {
        ConnectionStatus = status;
        StateHasChanged();
    }

    private async Task HandleActionCompleted()
    {
        if (connectionStatusComponent != null)
        {
            await connectionStatusComponent.RefreshAsync();
        }
    }

    private async Task HandleActionError(string errorMessage)
    {
        // Error is already handled by the action buttons component
        // We could add additional logging here if needed
    }

    private async Task RefreshStatusAsync()
    {
        if (connectionStatusComponent != null)
        {
            await connectionStatusComponent.RefreshAsync();
        }
    }

    private async Task ValidateTokenDetailsAsync()
    {
        try
        {
            IsLoading = true;
            LoadingMessage = "Validating Facebook token...";
            StateHasChanged();

            TokenValidation = await FacebookAuthService.ValidateCurrentTokenAsync();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error validating token: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private void NavigateToPages()
    {
        NavigationManager.NavigateTo("/facebook/pages");
    }

    private string GetRedirectUri()
    {
        var baseUri = NavigationManager.BaseUri.TrimEnd('/');
        return $"{baseUri}/facebook/connection";
    }
}
