@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookAuthService FacebookAuthService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager

<div class="facebook-connection-status">
    @if (IsLoading)
    {
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span>Checking Facebook connection...</span>
        </div>
    }
    else if (ConnectionStatus != null)
    {
        @if (ConnectionStatus.IsConnected && ConnectionStatus.IsTokenValid && !ConnectionStatus.RequiresReconnection)
        {
            <!-- Connected and Valid -->
            <Alert Color="Color.Success" Visible="true">
                <AlertMessage>
                    <Icon Name="IconName.CheckCircle" class="me-2" />
                    <strong>Facebook Connected</strong>
                </AlertMessage>
                <AlertDescription>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div>Connected as: <strong>@ConnectionStatus.FacebookUser?.FacebookName</strong></div>
                            @if (ConnectionStatus.TokenExpiresAt.HasValue)
                            {
                                <small class="text-muted">Token expires: @ConnectionStatus.TokenExpiresAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                            }
                        </div>
                        <div>
                            @if (ShowActions)
                            {
                                <Button Color="Color.Secondary" Size="Size.Small" Clicked="@(() => OnDisconnectClicked.InvokeAsync())">
                                    <Icon Name="IconName.Times" class="me-1" />
                                    Disconnect
                                </Button>
                            }
                        </div>
                    </div>
                </AlertDescription>
            </Alert>
        }
        else if (ConnectionStatus.IsConnected && ConnectionStatus.RequiresReconnection)
        {
            <!-- Connected but needs reconnection -->
            <Alert Color="Color.Warning" Visible="true">
                <AlertMessage>
                    <Icon Name="IconName.ExclamationTriangle" class="me-2" />
                    <strong>Facebook Connection Issue</strong>
                </AlertMessage>
                <AlertDescription>
                    <div>
                        @if (ConnectionStatus.IsTokenExpired)
                        {
                            <p class="mb-2">Your Facebook access token has expired and needs to be renewed.</p>
                        }
                        else if (ConnectionStatus.MissingScopes.Any())
                        {
                            <p class="mb-2">Your Facebook connection is missing required permissions:</p>
                            <ul class="mb-2">
                                @foreach (var scope in ConnectionStatus.MissingScopes)
                                {
                                    <li><code>@scope</code></li>
                                }
                            </ul>
                        }
                        else if (!string.IsNullOrEmpty(ConnectionStatus.ErrorMessage))
                        {
                            <p class="mb-2">@ConnectionStatus.ErrorMessage</p>
                        }
                        
                        @if (ShowActions)
                        {
                            <div class="d-flex gap-2">
                                <Button Color="Color.Primary" Size="Size.Small" Clicked="@(() => OnReconnectClicked.InvokeAsync())">
                                    <Icon Name="IconName.Redo" class="me-1" />
                                    Reconnect Facebook
                                </Button>
                                <Button Color="Color.Danger" Size="Size.Small" Outline="true" Clicked="@(() => OnRevokePermissionsClicked.InvokeAsync())">
                                    <Icon Name="IconName.Times" class="me-1" />
                                    Remove Connection
                                </Button>
                            </div>
                        }
                    </div>
                </AlertDescription>
            </Alert>
        }
        else
        {
            <!-- Not connected -->
            <Alert Color="Color.Info" Visible="true">
                <AlertMessage>
                    <Icon Name="IconName.Info" class="me-2" />
                    <strong>Facebook Not Connected</strong>
                </AlertMessage>
                <AlertDescription>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-0">Connect your Facebook account to enable auto-reply functionality for your Facebook Pages.</p>
                        </div>
                        <div>
                            @if (ShowActions)
                            {
                                <Button Color="Color.Primary" Size="Size.Small" Clicked="@(() => OnConnectClicked.InvokeAsync())">
                                    <Icon Name="IconName.Link" class="me-1" />
                                    Connect Facebook
                                </Button>
                            }
                        </div>
                    </div>
                </AlertDescription>
            </Alert>
        }
    }
</div>

@code {
    [Parameter] public bool ShowActions { get; set; } = true;
    [Parameter] public EventCallback OnConnectClicked { get; set; }
    [Parameter] public EventCallback OnReconnectClicked { get; set; }
    [Parameter] public EventCallback OnDisconnectClicked { get; set; }
    [Parameter] public EventCallback OnRevokePermissionsClicked { get; set; }
    [Parameter] public EventCallback<FacebookConnectionStatusDto> OnStatusChanged { get; set; }

    private FacebookConnectionStatusDto? ConnectionStatus { get; set; }
    private bool IsLoading { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadConnectionStatus();
    }

    public async Task RefreshAsync()
    {
        await LoadConnectionStatus();
        StateHasChanged();
    }

    private async Task LoadConnectionStatus()
    {
        try
        {
            IsLoading = true;
            ConnectionStatus = await FacebookAuthService.GetConnectionStatusAsync();
            
            if (OnStatusChanged.HasDelegate)
            {
                await OnStatusChanged.InvokeAsync(ConnectionStatus);
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading Facebook connection status: {ex.Message}");
            ConnectionStatus = new FacebookConnectionStatusDto
            {
                IsConnected = false,
                ErrorMessage = "Unable to check connection status"
            };
        }
        finally
        {
            IsLoading = false;
        }
    }
}

<style>
    .facebook-connection-status {
        margin-bottom: 1rem;
    }
    
    .facebook-connection-status .alert {
        margin-bottom: 0;
    }
    
    .facebook-connection-status code {
        font-size: 0.875em;
        background-color: rgba(0, 0, 0, 0.1);
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
    }
</style>
